import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { createPortal } from 'react-dom'
import { BarChart3, Check, Star, Zap, ChevronDown, User, LogOut } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card } from '../components/ui/Card'
import { useAuthStore } from '../stores/authStore'
import { useNavigate } from 'react-router-dom'
import { createCheckoutSession, pricingPlans } from '../lib/stripe'

interface PricingTier {
  name: string
  price: string
  reports: number
  features: string[]
  popular?: boolean
  pricePerReport: string
}

export const PricingPage: React.FC = () => {
  const { user, signOut } = useAuthStore()
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState<string | null>(null)
  const [showProfileDropdown, setShowProfileDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Handle profile dropdown
  const handleProfileClick = () => {
    if (!user) {
      navigate('/')
    } else {
      setShowProfileDropdown(!showProfileDropdown)
    }
  }

  const handleDashboardClick = () => {
    setShowProfileDropdown(false)
    navigate('/profile')
  }

  const handleSignOutClick = () => {
    setShowProfileDropdown(false)
    signOut()
    navigate('/')
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node
      
      // Check if click is on the profile button or dropdown menu
      const isProfileButton = dropdownRef.current?.contains(target)
      const isDropdownMenu = (target as Element)?.closest('[data-dropdown-menu]')
      
      if (!isProfileButton && !isDropdownMenu) {
        setShowProfileDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Cleanup loading state on unmount
  useEffect(() => {
    return () => {
      setIsLoading(null)
    }
  }, [])

  const pricingTiers: PricingTier[] = [
    {
      name: 'Starter',
      price: '$4.99',
      reports: 3,
      pricePerReport: '$1.66',
      features: [
        '3 comprehensive reports',
        'Multi-platform analysis',
        'AI-powered insights',
        'Export to PDF',
        'Email support'
      ]
    },
    {
      name: 'Professional',
      price: '$9.99',
      reports: 10,
      pricePerReport: '$1.00',
      popular: true,
      features: [
        '10 comprehensive reports',
        'Multi-platform analysis',
        'AI-powered insights',
        'Export to PDF',
        'Priority email support'
      ]
    },
    {
      name: 'Business',
      price: '$19.99',
      reports: 25,
      pricePerReport: '$0.80',
      features: [
        '25 comprehensive reports',
        'Multi-platform analysis',
        'AI-powered insights',
        'Export to PDF',
        'Priority email support'
      ]
    }
  ]

  const handleSelectPlan = async (tier: PricingTier) => {
    if (!user) {
      // Redirect to sign up/login
      navigate('/')
      return
    }

    // Prevent multiple simultaneous requests
    if (isLoading) {
      return
    }

    // Find matching Stripe plan
    const stripePlan = pricingPlans.find(plan => plan.name === tier.name)
    if (!stripePlan) {
      alert('Plan not found. Please try again.')
      return
    }

    setIsLoading(tier.name)
    
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 30000) // 30 second timeout
      })

      const sessionPromise = createCheckoutSession(
        stripePlan.priceId,
        user.id,
        tier.name,
        tier.reports
      )

      const session = await Promise.race([sessionPromise, timeoutPromise])

      if (session.url) {
        // Clear loading state before redirect to prevent stuck state
        setIsLoading(null)
        window.location.href = session.url
      } else {
        throw new Error('No checkout URL returned')
      }
    } catch (error) {
      console.error('Payment error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      alert(`Failed to initiate payment: ${errorMessage}`)
    } finally {
      // Ensure loading state is always reset
      setIsLoading(null)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
      {/* Header */}
      <header className="border-b border-white/10 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2 cursor-pointer"
            onClick={() => navigate('/')}
          >
            <BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 text-[#2DD4BF]" />
            <span className="text-lg sm:text-xl font-bold text-white">AppReview.Today</span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2 sm:space-x-4"
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="text-sm text-white/80 hover:text-white"
            >
              Home
            </Button>
            
            <div className="relative" ref={dropdownRef}>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleProfileClick}
                className="text-sm text-white/80 hover:text-white flex items-center space-x-1"
              >
                <span>{user ? 'Profile' : 'Sign In'}</span>
                {user && <ChevronDown className="w-3 h-3" />}
              </Button>
              
              {user && showProfileDropdown && createPortal(
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="fixed top-16 right-4 w-48 bg-[#1a2332] border border-white/10 rounded-lg shadow-lg z-[99999]"
                  style={{ zIndex: 99999 }}
                  data-dropdown-menu
                >
                  <div className="py-2">
                    <button
                      onClick={handleDashboardClick}
                      className="w-full text-left px-4 py-2 text-sm text-white hover:bg-white/5 flex items-center space-x-2"
                    >
                      <User className="w-4 h-4" />
                      <span>Dashboard</span>
                    </button>
                    <button
                      onClick={handleSignOutClick}
                      className="w-full text-left px-4 py-2 text-sm text-white hover:bg-white/5 flex items-center space-x-2"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Sign Out</span>
                    </button>
                  </div>
                </motion.div>,
                document.body
              )}
            </div>
          </motion.div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight"
          >
            Choose Your
            <span className="text-[#2DD4BF] block">Analysis Plan</span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg sm:text-xl text-white/70 mb-8 sm:mb-12 max-w-2xl mx-auto leading-relaxed px-4"
          >
            Get more insights with our affordable plans. Analyze user reviews across multiple platforms and generate comprehensive reports.
          </motion.p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-12 px-4 sm:px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-6 lg:gap-8">
            {pricingTiers.map((tier, index) => (
              <motion.div
                key={tier.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                className="relative"
              >
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-gradient-to-r from-[#2DD4BF] to-[#14B8A6] text-[#0A1128] px-4 py-1 rounded-full text-sm font-semibold flex items-center space-x-1">
                      <Star className="w-4 h-4" />
                      <span>Most Popular</span>
                    </div>
                  </div>
                )}
                
                <Card 
                  hover 
                  className={`text-center h-full ${
                    tier.popular 
                      ? 'border-[#2DD4BF]/30 bg-gradient-to-b from-[#2DD4BF]/5 to-transparent' 
                      : ''
                  }`}
                >
                  <div className="mb-6">
                    <h3 className="text-xl sm:text-2xl font-bold text-white mb-2">
                      {tier.name}
                    </h3>
                    <div className="text-3xl sm:text-4xl font-bold text-[#2DD4BF] mb-2">
                      {tier.price}
                    </div>
                    <div className="text-white/60 text-sm">
                      {tier.reports} reports • {tier.pricePerReport} per report
                    </div>
                  </div>

                  <ul className="space-y-3 mb-8 text-left">
                    {tier.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start space-x-3">
                        <Check className="w-5 h-5 text-[#2DD4BF] mt-0.5 flex-shrink-0" />
                        <span className="text-white/80 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    variant={tier.popular ? 'primary' : 'secondary'}
                    size="lg"
                    onClick={() => handleSelectPlan(tier)}
                    disabled={isLoading === tier.name}
                    className="w-full"
                  >
                    {isLoading === tier.name ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <span>Processing...</span>
                      </div>
                    ) : (
                      <>
                        {tier.popular && <Zap className="w-5 h-5 mr-2" />}
                        Choose {tier.name}
                      </>
                    )}
                  </Button>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 sm:py-20 px-4 sm:px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-6">
              All Plans Include
            </h2>
            
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {[
                'App Store Reviews',
                'Google Play Reviews', 
                'Reddit Discussions',
                'AI-Powered Analysis'
              ].map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-12 h-12 bg-[#2DD4BF]/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Check className="w-6 h-6 text-[#2DD4BF]" />
                  </div>
                  <span className="text-white/80 font-medium">{feature}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
          >
            <Card className="bg-gradient-to-r from-[#2DD4BF]/10 to-[#14B8A6]/10 border-[#2DD4BF]/20">
              <h2 className="text-2xl sm:text-3xl font-bold text-white mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-white/70 mb-6 sm:mb-8 text-base sm:text-lg px-4">
                Start generating comprehensive app review analyses today
              </p>
              <Button
                size="lg"
                onClick={() => navigate(user ? '/profile' : '/')}
                className="text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 w-full sm:w-auto"
              >
                {user ? 'Go to Profile' : 'Sign Up Now'}
              </Button>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-6 sm:py-8 px-4 sm:px-6">
        <div className="max-w-6xl mx-auto text-center text-white/60">
          <p className="text-sm sm:text-base">&copy; 2025 AppReview.Today. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
} 