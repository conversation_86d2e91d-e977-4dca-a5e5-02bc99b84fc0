import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Analytics } from '@vercel/analytics/react'
import { useAuthStore } from './stores/authStore'
import { LandingPage } from './pages/LandingPage'
import { ReportPage } from './pages/ReportPage'
import { ProfilePage } from './pages/ProfilePage'
import { DemoPage } from './pages/DemoPage'
import { PricingPage } from './pages/PricingPage'
import { PaymentSuccessPage } from './pages/PaymentSuccessPage'
import { LoadingSpinner } from './components/LoadingSpinner'
import { PaymentStatusBanner } from './components/PaymentStatusBanner'

const queryClient = new QueryClient()

function App() {
  const { initialize, loading } = useAuthStore()

  useEffect(() => {
    initialize()
  }, [initialize])

  if (loading) {
    return <LoadingSpinner message="Initializing..." />
  }

  return (
    <QueryClientProvider client={queryClient}>
      <Router
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <PaymentStatusBanner />
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/pricing" element={<PricingPage />} />
          <Route path="/payment-success" element={<ProtectedRoute><PaymentSuccessPage /></ProtectedRoute>} />
          <Route path="/report/:reportId" element={<ReportPage />} />
          <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
          <Route path="/demo" element={<DemoPage />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
      <Analytics />
    </QueryClientProvider>
  )
}

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuthStore()
  
  // Show loading while authentication is being checked
  if (loading) {
    return <LoadingSpinner message="Checking authentication..." />
  }
  
  // Only redirect if we're sure the user is not authenticated
  if (!user) {
    return <Navigate to="/" replace />
  }
  
  return children
}

export default App